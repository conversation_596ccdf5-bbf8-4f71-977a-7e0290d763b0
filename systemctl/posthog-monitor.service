[Unit]
Description=PostHog VM Statistics Monitoring Service
After=network.target

[Service]
# User to run the script as
User=coloraria
Group=coloraria

# Define the environment variable for the API key here
Environment="POSTHOG_API_KEY=phc_1dd8iqXdRedejlLBDMWheCQTSXWwvmP0QS7nACzFifh"

# Absolute path to your script with the "while true" loop
ExecStart=/home/<USER>/.config/systemd/user/posthog_monitor.sh

# Restart the service automatically if it ever fails
Restart=always
RestartSec=10

[Install]
WantedBy=default.target